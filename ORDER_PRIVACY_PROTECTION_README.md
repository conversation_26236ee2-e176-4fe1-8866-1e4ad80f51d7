# 订单隐私保护功能说明

## 功能概述

订单隐私保护功能是为了保护代理商客户的隐私信息而开发的安全功能。当此功能开启时，主站无法通过代理网站客户下单时预留的信息（如客户姓名、电话、邮箱等）来查询和显示这些订单，有效保护代理商客户的隐私。

## 主要特性

1. **功能开关控制**：管理员可以在后台设置中方便地开启或关闭此功能
2. **订单查询过滤**：开启后，前台订单查询将过滤掉代理商订单
3. **敏感信息脱敏**：对代理商订单的敏感信息进行脱敏显示
4. **实时生效**：功能开关状态实时生效，无需重启系统

## 安装步骤

### 1. 数据库更新

执行以下SQL语句添加配置项：

```sql
-- 对于新安装的系统，配置项已包含在安装脚本中
-- 对于现有系统，请执行以下语句：
INSERT IGNORE INTO `cmy_config` (`k`, `v`) VALUES ('order_privacy_protection', '0');
```

或者执行提供的更新脚本：
```bash
mysql -u用户名 -p密码 数据库名 < instakk/update_privacy_protection.sql
```

### 2. 文件更新

确保以下文件已更新：
- `includes/common.php` - 添加了脱敏处理函数
- `ajax.php` - 修改了订单查询逻辑
- `AGxiaomin9898/set.php` - 添加了后台设置界面
- `AGxiaomin9898/list-table.php` - 修改了订单列表显示
- `sup/orders/list.php` - 修改了供货商后台显示

## 使用方法

### 1. 开启/关闭功能

1. 登录后台管理系统
2. 进入"系统设置" -> "网站订单相关设置"
3. 找到"订单隐私保护"选项
4. 选择"开启"或"关闭"
5. 点击"修改"保存设置

### 2. 功能效果

**关闭状态（默认）：**
- 所有订单正常显示和查询
- 不进行任何隐私保护处理

**开启状态：**
- 前台订单查询时，代理商客户的订单（zid > 1）将被过滤，无法通过客户信息查询到代理订单
- 主站用户的订单（zid = 1）正常显示和查询
- 前台订单查询结果中，代理商客户订单的敏感信息将被脱敏显示
- 后台订单列表正常显示所有信息，不受影响（管理员需要查看完整信息进行管理）
- 脱敏格式示例：
  - 手机号：138****1234
  - 邮箱：abc***@example.com
  - QQ号：123***789
  - 其他信息：ab***cd

## 技术实现

### 1. 核心函数

- `maskSensitiveData($data, $type)` - 敏感信息脱敏处理
- `shouldMaskOrderData($order)` - 判断订单是否需要脱敏

### 2. 判断逻辑

系统通过订单表的`zid`字段判断订单来源：
- `zid = 1`：主站用户订单，不受影响，正常显示和查询
- `zid > 1`：代理商客户订单，受隐私保护影响

### 3. 脱敏规则

- **手机号**：保留前3位和后4位，中间用****替代
- **邮箱**：用户名部分保留前3位，其余用***替代
- **QQ号**：保留前3位和后3位，中间用***替代
- **通用文本**：保留前后各2个字符，中间用***替代

## 测试验证

运行测试脚本验证功能：
```
访问：http://你的域名/test_privacy_protection.php
```

测试完成后请删除测试文件。

## 注意事项

1. **功能范围**：此功能仅影响前台订单查询，后台订单列表管理功能不受影响，始终显示完整信息
2. **管理权限**：后台管理员可以查看所有订单的完整信息，便于进行订单管理和客服处理
3. **性能影响**：脱敏处理对性能影响极小，可放心使用
4. **兼容性**：功能向下兼容，不影响现有订单数据和管理流程
5. **备份建议**：启用前建议备份数据库和相关文件

## 故障排除

### 1. 配置项不存在
如果提示配置项不存在，请执行数据库更新语句。

### 2. 脱敏不生效
检查以下项目：
- 确认功能已开启
- 确认订单的sid字段值大于0
- 检查相关文件是否正确更新

### 3. 查询异常
如果订单查询出现异常，请检查：
- 数据库连接是否正常
- SQL语句是否正确执行
- 错误日志中的具体信息

## 版本信息

- 版本：1.0.0
- 开发日期：2025-08-06
- 兼容系统：云小店系统
- 作者：AI Assistant

## 技术支持

如遇到问题，请检查：
1. 数据库配置是否正确
2. 文件权限是否正常
3. 系统日志中的错误信息

建议在测试环境中先行验证功能正常后再部署到生产环境。
