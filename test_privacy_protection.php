<?php
/**
 * 订单隐私保护功能测试脚本
 * 用于验证隐私保护功能是否正常工作
 */

include 'includes/common.php';

echo "<h1>订单隐私保护功能测试</h1>";

// 测试1：检查配置项是否存在
echo "<h2>测试1：检查配置项</h2>";
$privacy_config = $conf['order_privacy_protection'] ?? null;
if ($privacy_config !== null) {
    echo "✓ 配置项 order_privacy_protection 存在，当前值：" . $privacy_config . "<br>";
} else {
    echo "✗ 配置项 order_privacy_protection 不存在<br>";
}

// 测试2：测试脱敏函数
echo "<h2>测试2：脱敏函数测试</h2>";

$test_data = [
    '13812345678' => 'phone',
    '<EMAIL>' => 'email', 
    '123456789' => 'qq',
    'normaltext' => 'general'
];

foreach ($test_data as $data => $type) {
    $masked = maskSensitiveData($data, $type);
    echo "原始数据：{$data} -> 脱敏后：{$masked} (类型：{$type})<br>";
}

// 测试3：自动识别类型
echo "<h3>自动识别类型测试：</h3>";
$auto_test_data = ['13812345678', '<EMAIL>', '987654321', 'sometext'];
foreach ($auto_test_data as $data) {
    $masked = maskSensitiveData($data, 'auto');
    echo "原始数据：{$data} -> 脱敏后：{$masked}<br>";
}

// 测试4：检查订单是否需要脱敏
echo "<h2>测试3：订单脱敏判断测试</h2>";

$test_orders = [
    ['zid' => 1, 'input' => '13812345678'],  // 主站订单
    ['zid' => 2, 'input' => '13812345678'],  // 代理商客户订单
    ['zid' => 1000, 'input' => '13812345678'] // 其他分站客户订单
];

// 模拟主站环境
$is_fenzhan = false;
echo "<h3>主站环境 - 前台查询模式：</h3>";
foreach ($test_orders as $index => $order) {
    $should_mask = shouldMaskOrderData($order, false);
    echo "订单" . ($index + 1) . " (zid={$order['zid']}): " . ($should_mask ? "需要脱敏" : "不需要脱敏") . "<br>";
}

// 模拟分站环境
$is_fenzhan = true;
echo "<h3>分站环境 - 前台查询模式：</h3>";
foreach ($test_orders as $index => $order) {
    $should_mask = shouldMaskOrderData($order, false);
    echo "订单" . ($index + 1) . " (zid={$order['zid']}): " . ($should_mask ? "需要脱敏" : "不需要脱敏") . "<br>";
}

// 恢复主站环境
$is_fenzhan = false;
echo "<h3>后台管理模式：</h3>";
foreach ($test_orders as $index => $order) {
    $should_mask = shouldMaskOrderData($order, true);
    echo "订单" . ($index + 1) . " (zid={$order['zid']}): " . ($should_mask ? "需要脱敏" : "不需要脱敏") . "<br>";
}

// 测试5：模拟不同配置下的行为
echo "<h2>测试4：不同配置下的行为</h2>";

// 保存原始配置
$original_config = $conf['order_privacy_protection'];

// 测试关闭状态
$conf['order_privacy_protection'] = 0;
$test_order = ['zid' => 2, 'input' => '13812345678'];
$should_mask_off = shouldMaskOrderData($test_order);
echo "隐私保护关闭时，代理商客户订单：" . ($should_mask_off ? "需要脱敏" : "不需要脱敏") . "<br>";

// 测试开启状态
$conf['order_privacy_protection'] = 1;
$should_mask_on = shouldMaskOrderData($test_order);
echo "隐私保护开启时，代理商客户订单：" . ($should_mask_on ? "需要脱敏" : "不需要脱敏") . "<br>";

// 恢复原始配置
$conf['order_privacy_protection'] = $original_config;

echo "<h2>测试完成</h2>";
echo "<p>请检查以上测试结果，确保所有功能正常工作。</p>";
echo "<p><strong>注意：</strong>此测试脚本仅用于开发测试，生产环境中请删除此文件。</p>";
?>
