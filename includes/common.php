<?php
if (version_compare(PHP_VERSION, '8.0.20') == 1) {
    die('当前服务器环境php版本高于8.0.20, 请使用7.0~8.0的版本以保障稳定性');
}

if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    die('当前服务器环境php版本小于7.0, 请使用7.0~8.0的版本以保障稳定性');
}

register_shutdown_function('callError');

$webConfig = [];
if (file_exists(__DIR__ . '/config.php')) {
    $webConfig = include __DIR__ . '/config.php';
}

$debug = false;
if (isset($webConfig['debug']) && $webConfig['debug'] && (!isset($_GET['act']) || $_GET['act'] != 'updateNew')) {
    $debug = true;
    error_reporting(E_ERROR | E_PARSE | E_COMPILE_ERROR);
} else if (isset($_GET['debug']) && $_GET['debug'] == 'ok') {
    $debug = true;
    error_reporting(E_ERROR | E_PARSE | E_COMPILE_ERROR);
} else {
    error_reporting(0);
}

define('CACHE_FILE', 0);
define('ENCRYPT_KEY', md5('!|@#$%^&|=$%&#@$#%$*'));
define('IN_CRONLITE', true);
define('SYSTEM_ROOT', dirname(__FILE__) . '/');
define('ROOT', dirname(SYSTEM_ROOT) . '/');
define('TEMPLATE_ROOT', ROOT . 'template/');
define('PLUGIN_ROOT', ROOT . 'includes/plugin/');
if (array_key_exists('HTTPS', $_SERVER) && ($_SERVER['HTTPS'] === 1 || $_SERVER['HTTPS'] === 'on') || array_key_exists('SERVER_PORT', $_SERVER) && $_SERVER['SERVER_PORT'] === 443) {
    define('HTTPS_ROOT', true);
}
!defined('HTTPS_ROOT') && define('HTTPS_ROOT', false);

if (isset($_POST)) {
    define('IS_POST', true);
} else {
    define('IS_POST', false);
}

$content_type = '';
if (isset($_SERVER['HTTP_CONTENT_TYPE'])) {
    $content_type = $_SERVER['HTTP_CONTENT_TYPE'];
} elseif (isset($_SERVER['CONTENT_TYPE'])) {
    $content_type = $_SERVER['CONTENT_TYPE'];
} elseif (isset($_SERVER['HTTP_ACCEPT'])) {
    $content_type = $_SERVER['HTTP_ACCEPT'];
}
// 检查请求是否包含JSON数据
if (strpos($content_type, 'application/json') !== false) {
    define('IS_AJAX', true);
    // 从php://input读取原始输入流
    $json = file_get_contents('php://input');
    // 尝试解析JSON数据
    $data = json_decode($json, true); // 第二个参数为true时，返回数组而非对象
    if (json_last_error() === JSON_ERROR_NONE) {
        // 假设我们只处理第一层级的键值对
        foreach ($data as $key => $value) {
            // 注意：这样做可能会覆盖已存在的$_POST数据
            $_POST[$key] = $value;
        }
    }
} else {
    if (isset($_SERVER["HTTP_X_REQUESTED_WITH"]) && strtolower($_SERVER["HTTP_X_REQUESTED_WITH"]) == "xmlhttprequest") {
        define('IS_AJAX', true);
    } else {
        define('IS_AJAX', false);
    }
}

date_default_timezone_set('PRC');
$date          = date('Y-m-d H:i:s');
$strSafeOpen   = false;
$password_hash = '!@#%!s!0';

session_start();

$version_info = ['version' => '1.0.6', 'build_core' => '1006', 'build_sql' => 20221003001];
if (file_exists(SYSTEM_ROOT . 'version.php')) {
    $version_info = include SYSTEM_ROOT . 'version.php';
    $SYSVERSION   = $version_info['version'];
    $version      = $version_info['build_core'];
} else {
    $SYSVERSION = '1.0.6';
    $version    = '1006';
}

include_once SYSTEM_ROOT . 'autoloader.php';
Autoloader::register();
include_once SYSTEM_ROOT . 'authcode.php';
define('authcode', $authcode);
define('DIST_ID', hexdec($distid));
if (!defined('authcode') || !preg_match('/^[\w]{32,}$/', $authcode)) {
    showErrPage("授权文件缺失或已损坏，请在官网下载更新包覆盖");
}

$is_fenzhan = false;
$siterow    = [];

if (is_file(SYSTEM_ROOT . '360safe/360webscan.php')) {
    include_once SYSTEM_ROOT . '360safe/360webscan.php';
}
$scriptpath = str_replace('\\', '/', $_SERVER['SCRIPT_NAME']);
$sitepath   = substr($scriptpath, 0, strrpos($scriptpath, '/'));
$runurl     = (HTTPS_ROOT ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . $sitepath . '/';

if (!file_exists(ROOT . 'dbconfig.php')) {
    if (!is_file('api.php') && !is_file('dbconfig.php')) {
        $runurl = substr(rtrim($runurl, '/'), 0, strripos(rtrim($runurl, '/'), '/')) . '/';
        @header("Location: " . $runurl . "install/");
    }
    @header("Location: " . $runurl . "install/");
}

$weburl  = (HTTPS_ROOT ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . $sitepath . '/';
$siteurl = $weburl;

require_once ROOT . 'dbconfig.php';

if (!$dbconfig['dbuser'] || !$dbconfig['dbpwd'] || !$dbconfig['dbname']) {
    header('Content-type:text/html;charset=utf-8');
    if (!is_file('dbconfig.php')) {
        echo '你还没安装[0]！<a href="' . getWebPath() . 'install/">点此安装</a>';
    } else {
        echo '你还没安装[01]！<a href="' . getWebPath() . 'install/">点此安装</a>';
    }
    exit(0);
}

try {
    $DB = new \core\PdoReger($dbconfig);
} catch (\Exception $e) {
    if (strpos($e->getMessage(), '1045') !== false) {
        showErrPage("数据库连接失败，请检查数据库信息账号密码等是否正确！");
    }
    showErrPage("系统错误，" . $e->getMessage());
}

if ($DB->query('SELECT * from `pre_config`') == false) {
    header('Content-type:text/html;charset=utf-8');
    echo '你还没安装[1]！<a href="' . getWebPath() . 'install/">点此安装</a>';
    exit(0);
}

$CACHE = new \core\Cache();
$conf  = $CACHE->config();

if (!IS_AJAX && !is_array($conf) || !isset($conf['sitename']) || !isset($conf['adm_user'])) {
    showErrPage('系统配置数据出错，请将程序中的[install/sql/config.sql]导入到数据库尝试恢复<br/>请注意！导入后后台账号密码将恢复到默认账号【admin/123456】<br/>注意，如果导入后还是未恢复，请重新安装本程序');
}

if (file_exists(SYSTEM_ROOT . 'base.php')) {
    include_once SYSTEM_ROOT . 'base.php';
}

!defined('CC_Defender') && define('CC_Defender', 1);
if (isset($is_defend) && $is_defend || CC_Defender > 1) {
    $is_whited = checkIsWhite();
    if (CC_Defender == 3 && $is_whited === false) {
        cc_defender(3);
    } elseif (CC_Defender == 1 && $is_whited === false && check_spider() === false) {
        cc_defender(1);
    } elseif (CC_Defender == 2 && $is_whited === false) {
        cc_defender(2);
    }
}

if (empty($conf['syskey'])) {
    $conf['syskey'] = md5($date);
    $DB->query("REPLACE INTO `pre_config` SET v= ?,k='syskey'", [$conf['syskey']]);
}

// if ($conf['version'] != $version) {
//     $conf['version'] = $version;
//     $DB->query("REPLACE INTO `pre_config` SET v= ?,k='version'", [$conf['version']]);
// }

if (empty($conf['index_jsver'])) {
    $conf['index_jsver'] = date("YmdHis");
    $DB->query("REPLACE INTO `pre_config` SET v= ?,k='index_jsver'", [$conf['index_jsver']]);
}

$jsver = '?v=' . $conf['version'] . '&j=' . $conf['index_jsver'];

define('VERSION', $jsver);
define('SYS_KEY', $conf['syskey']);

if ($conf['qqjump'] > 0 && checkTenBrowser()) {
    include TEMPLATE_ROOT . '/default/jump.php';
    exit(0);
}

if (isset($is_defend) && $is_defend || CC_Defender > 1) {
    check_spider() === false && checkTen();
}

/**
 * 错误处理
 */
function callError()
{
    if (isset($_GET['act']) && $_GET['act'] == 'updateNew') {
        //正在更新时忽略错误
        return true;
    }
    if ($error = error_get_last()) {
        $type = $error['type'];
        die($type);
        $message = str_replace(ROOT, '', $error['message']);
        $message = str_replace(dirname(ROOT), '', $message);
        $trace   = '';
        if (stripos($message, 'trace:')) {
            $arr   = explode('trace:', $message);
            $msg   = str_ireplace('trace:', '', $arr[0]);
            $trace = $arr[1];
        } else {
            $msg = $message;
            if (function_exists('debug_backtrace')) {
                $trace_arr = debug_backtrace();
                if (is_array($trace_arr)) {
                    foreach ($trace_arr as $key => $row) {
                        $trace .= str_replace(ROOT, '', $row['file']) . '[' . $row['line'] . ']：' . $row['function'];
                    }
                }
            } elseif (function_exists('error_get_last')) {
                $trace = str_replace(ROOT, '', (string) error_get_last());
            }
        }

        $file = str_replace(ROOT, '', $error['file']);
        $file = str_replace(dirname(ROOT), '', $file);
        $line = $error['line'];

        if (in_array($type, [E_ERROR])) {
            $LOG = new \core\Log(1, 60, 'Error');
            $msg = "{$file}[{$line}]<br/>{$msg}";
            if ($trace) {
                $msg .= "<br/><br/>调用堆栈：{$trace}";
            }

            if (is_object($LOG) && method_exists($LOG, 'add')) {
                $LOG->add('错误日志', $msg . PHP_EOL);
            }
            if (IS_AJAX) {
                exit(json_encode(['code' => -1, 'msg' => $file . "[" . $line . "]<br/>" . $msg]));
            }
            $msg .= "<br/><br/>参考建议：如更新系统、安装插件后出错可能文件缺少导致，可尝试更换php版本、下载更新版覆盖解决";
            if (in_array($type, [E_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
                showErrPage($msg);
            }
        }
    }
}

function isIe()
{
    $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    if (preg_match('/msie\s([^\s|;|\)]+)/', $agent)) {
        return true;
    } elseif (preg_match('/trident/', $agent)) {
        return true;
    } elseif (preg_match('/rv:/', $agent)) {
        return true;
    }
    return false;
}
function getIeVersion()
{
    if (isIe()) {
        $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
        if (strpos($agent, 'msie') !== false) {
            preg_match('/msie\s([^\s|;|\)]+)/i', $agent, $MSIERegs);
            return $MSIERegs[1];
        } elseif (strpos($agent, 'trident') !== false && strpos($agent, 'rv:') !== false) {

            preg_match('/rv:([^\s|;|\)]+)/i', $agent, $rvRegs);
            return $rvRegs[1];
        }
        return false;
    }
    return false;
}

function checkIsWhite()
{
    global $conf;
    $key = '';
    if (isset($_GET['apikey']) && $_GET['apikey']) {
        $key = $_GET['apikey'];
    } elseif (isset($_POST['apikey']) && $_POST['apikey']) {
        $key = $_POST['apikey'];
    } elseif (isset($_POST['key']) && $_POST['key']) {
        $key = $_POST['key'];
    } elseif (isset($_GET['key']) && $_GET['key']) {
        $key = $_GET['key'];
    }
    if ($conf['apikey'] === $key) {
        return true;
    } elseif ($conf['cronkey'] === $key) {
        return true;
    } elseif (isset($_SESSION['xl_t_ken']) && preg_match('/^[a-zA-Z0-9]{32}$/', $_SESSION['xl_t_ken'])) {
        if (isset($_GET[md5('c1m2d3s4' . authcode . $_SESSION['xl_t_ken'])])) {
            return true;
        }
    }
    return false;
}

function showErrPage($msg = '系统出错或正在升级，请稍候再访问！', $title = '页面出现500错误')
{
    global $conf;
    $sitename = $conf['sitename'];
    @header("Content-Type:text/html; charset=UTF-8");
    $html = <<<'text'
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
text;
    $html .= "<title>{$title}-{$sitename}</title>";
    $html .= base64_decode('PHN0eWxlPgogICAgaHRtbHtmb250LXNpemU6MTBweDt9LmNvbnRhaW5lcnt3aWR0aDo2MCU7bWFyZ2luOjglIGF1dG8gMDtiYWNrZ3JvdW5kLWNvbG9yOiNmMGYwZjA7cGFkZGluZzoyJSA1JTtib3JkZXItcmFkaXVzOjEwcHh9dWx7cGFkZGluZy1sZWZ0OjIwcHg7fXVsIGxpe2xpbmUtaGVpZ2h0OjIuM30uY29udGFpbmVyIGgxe2ZvbnQtc2l6ZTo0LjByZW07fS5jb250YWluZXIgaDN7Zm9udC1zaXplOjIuNHJlbTt9dWwgbGl7Zm9udC1zaXplOjIuMHJlbTt9dWwgbGkgYXtmb250LXNpemU6Mi41cmVtO31AbWVkaWEgc2NyZWVuIGFuZCAobWF4LXdpZHRoOjk5MnB4KXtodG1se2ZvbnQtc2l6ZToxNnB4O30uY29udGFpbmVye3dpZHRoOjg1JTttYXJnaW46NjBweCBhdXRvIDA7YmFja2dyb3VuZC1jb2xvcjojZjBmMGYwO3BhZGRpbmc6MTBweCAzMHB4O30uY29udGFpbmVyIGgxe2ZvbnQtc2l6ZToxLjhyZW07cGFkZGluZzoxMHB4IDZweDttYXJnaW4tdG9wOjI1cHg7fS5jb250YWluZXIgaDN7Zm9udC1zaXplOjEuMnJlbTtwYWRkaW5nOjhweCAzcHg7bWFyZ2luLXRvcDoxMnB4O311bHtwYWRkaW5nOjVweCAzcHg7fXVsIGxpe2ZvbnQtc2l6ZTowLjdyZW07fXVsIGxpLmJ0bntwYWRkaW5nOjE1cHg7fXVsIGxpLmJ0biBhe2ZvbnQtc2l6ZTowLjlyZW07fX1he2NvbG9yOiMyMGE1M2F9CiAgICA8L3N0eWxlPgo8L2hlYWQ+Cjxib2R5PgogICAgPGRpdiBjbGFzcz0iY29udGFpbmVyIj4KICAgICAgICA8aDE+57O757uf5Ye6546w6ZSZ6K+vPC9oMT4KICAgICAgICA8aDM+6ZSZ6K+v5L+h5oGv77ya');
    $html .= $msg;
    $html .= base64_decode('PC9oMz4KICAgICAgICA8dWw+CiAgICAgICAgICAgIDxsaT7lh7rnjrDor6Xmj5DnpLror7TmmI7kvaDopoHorr/pl67nmoTpobXpnaLpgYfliLDoh7Tlkb3mgKfplJnor6/vvIE8L2xpPgogICAgICAgICAgICA8bGk+5aaC5p6c5oKo5LiN5piv572R56uZ566h55CG5ZGY77yM6K+35Y+C54Wn5LiL5pa56K+05piO5pON5L2cPC9saT4KICAgICAgICAgICAgPGxpPuWmguaenOaCqOS7jemcgOimgeiuv+mXruebruagh+mhtemdou+8jOWPr+S7peWFiOiHquihjOiBlOezu+e9keermeeuoeeQhuWRmOino+WGs++8jOiBlOezu1FR');
    $html .= $conf['zzqq'];
    $html .= base64_decode('PC9saT4KICAgICAgICAgICAgPGxpPuaCqOi/mOWPr+S7peWwneivleetieWHoOWIhumSn+WGjeiuv+mXru+8jOaIluiAhei/lOWbnuS5i+WJjeeahOmhtemdoumHjeivlTwvbGk+CiAgICAgICAgICAgIDxsaSBjbGFzcz0iYnRuIj48YSBocmVmPSJqYXZhc2NyaXB0Omhpc3RvcnkuYmFjaygtMSkiPjw8POi/lOWbnuS4iuS4gOmhtTwvYT4mbmJzcDt8Jm5ic3A7PGEgaHJlZj0iLyI+572R56uZ6aaW6aG1PC9hPjwvbGk+CiAgICAgICAgPC91bD4KICAgIDwvZGl2Pgo8L2JvZHk+CjwvaHRtbD4=');
    echo $html;
    die;
}

// if ($conf['version'] == "" && $DB->get_column("SELECT v FROM cmy_config WHERE k='version' LIMIT 1") == "") {
//     header('Content-type:text/html;charset=utf-8');
//     echo '请先完成网站升级！<a href="../install/update.php"><font color=red>点此升级</font></a>';
//     exit(0);
// }

empty($conf['index_ssapi']) && $conf['index_ssapi'] = '1';

($conf['zzqq'] == "" || !is_numeric($conf['zzqq'])) && $conf['zzqq'] = $conf['kfqq'];

if ($conf['cloud_api'] == 2) {
    if (empty($conf['cloud_app_id'])) {
        $conf['cloud_open']     = 0;
        $conf['cloud_open_all'] = 0;
    }
} elseif ($conf['cloud_api'] == 1) {
    if (empty($conf['cloud_user'])) {
        $conf['cloud_open']     = 0;
        $conf['cloud_open_all'] = 0;
    }
} else {
    $conf['cloud_open']     = 0;
    $conf['cloud_open_all'] = 0;
}

if (empty($conf['data_type'])) {
    $conf['data_type'] = 1;
    $DB->query("update cmy_config set v='1' WHERE k='data_type' LIMIT 1");
}

function checkTen()
{
    global $conf, $password_hash;
    //IP屏蔽
    $iptables     = '1872850689~1872850943|1700480345|2073511781|3082860377|989548909|992246182|1882984123|3419255553~3419255807|3419274679|3419274497~3419274751|236000768~236001023|992312699|3419245824~3419246079|1728519168~1728520191';
    $remoteiplong = bindec(decbin(ip2long(x_real_ip())));
    foreach (explode('|', $iptables) as $iprows) {
        if ($remoteiplong == $iprows) {
            exit('网站建设中！');
        }
        $ipbanrange = explode('~', $iprows);
        if ($remoteiplong >= $ipbanrange[0] && $remoteiplong <= $ipbanrange[1]) {
            exit('欢迎访问！');
        }
    }

    if (strpos($_SERVER['HTTP_REFERER'], 'urls.tr.com') !== false) {
        exit('欢迎光临！');
    }

    $is_whited = checkIsWhite();

    if ($is_whited != true) {
        if (!isset($_SERVER['HTTP_ACCEPT']) || preg_match("/manager/", strtolower($_SERVER['HTTP_USER_AGENT'])) || isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] == '' || strpos($_SERVER['HTTP_USER_AGENT'], 'ozilla') !== false && strpos($_SERVER['HTTP_USER_AGENT'], 'Mozilla') === false || preg_match('/Windows NT 5\.1/', $_SERVER['HTTP_USER_AGENT']) && $_SERVER['HTTP_ACCEPT'] == '*/*' || preg_match('/vnd\.wap\.wml/', $_SERVER['HTTP_ACCEPT']) && preg_match('/Windows NT 5\.1/', $_SERVER['HTTP_USER_AGENT']) || isset($_COOKIE['ASPSESSIONIDQASBQDRC']) || empty($_SERVER['HTTP_USER_AGENT']) || preg_match('/Alibaba\.Security\.Heimdall/', $_SERVER['HTTP_USER_AGENT']) || strpos($_SERVER['HTTP_USER_AGENT'], 'wechatdevtools/') !== false || strpos($_SERVER['HTTP_USER_AGENT'], 'libcurl/') !== false || strpos($_SERVER['HTTP_USER_AGENT'], 'python') !== false || strpos($_SERVER['HTTP_USER_AGENT'], 'Go-http-client') !== false || $_SESSION['txprotectblock'] == true) {
            exit('感谢访问！');
        }
    }
}

function checkTenBrowser()
{
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
    if (stripos($userAgent, 'qq/') !== false) {
        return true;
    } elseif (stripos($userAgent, 'micromessenger') !== false) {
        return true;
    } elseif (stripos($userAgent, 'qqtheme/') !== false) {
        return true;
    } elseif (stripos($userAgent, 'and_sq/') !== false) {
        return true;
    }
    return false;
}

function x_real_ip()
{
    global $webConfig;
    $ip = $_SERVER['REMOTE_ADDR'];
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && preg_match_all("#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s", $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
        foreach ($matches[0] as $xip) {
            if (!preg_match("/^(10|172\.16|127|192\.168)\./", $xip)) {
                $ip = $xip;
            }
        }
    } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CF_CONNECTING_IP'])) {
        $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
    } else {
        if ((isset($_SERVER['HTTP_X_REAL_IP']) && preg_match("/^([0-9]{1,3}\.){3}[0-9]{1,3}$/", $_SERVER['HTTP_X_REAL_IP']))) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        }
    }

    if (checkIsWhite() === false && $webConfig['localhost'] !== true) {
        $regList = array(
            '/^10\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/',
            '/^192\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/',
            '/^127\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/',
        );
        foreach ($regList as $key => $value) {
            if (preg_match($value, $ip)) {
                die('<center><h3 style="padding-top:60px;">提示，请不要使用代理服务器设备访问本站！</h3></center>');
            }
        }
    }
    return $ip;
}

function check_spider()
{
    $useragent = strtolower($_SERVER['HTTP_USER_AGENT']);
    if (strpos($useragent, 'baiduspider/2.0') !== false && strpos($useragent, 'baidu.com/search/spider') !== false) {
        return 'baiduspider';
    }
    if (strpos($useragent, 'googlebot') !== false) {
        return 'googlebot';
    }
    if (strpos($useragent, '360spider') !== false) {
        return '360spider';
    }
    if (strpos($useragent, 'soso') !== false) {
        return 'soso';
    }
    if (strpos($useragent, 'bing') !== false) {
        return 'bing';
    }
    if (strpos($useragent, 'yahoo') !== false) {
        return 'yahoo';
    }
    if (strpos($useragent, 'sohu-search') !== false) {
        return 'Sohubot';
    }
    if (strpos($useragent, 'sogou') !== false) {
        return 'sogou';
    }
    if (strpos($useragent, 'youdaobot') !== false) {
        return 'YoudaoBot';
    }
    if (strpos($useragent, 'robozilla') !== false) {
        return 'Robozilla';
    }
    if (strpos($useragent, 'msnbot') !== false) {
        return 'msnbot';
    }
    if (strpos($useragent, 'lycos') !== false) {
        return 'Lycos';
    }
    if (!strpos($useragent, 'ia_archiver') === false) {
    } elseif (!strpos($useragent, 'iaarchiver') === false) {
        return 'alexa';
    }
    if (strpos($useragent, 'archive.org_bot') !== false) {
        return 'Archive';
    }
    if (strpos($useragent, 'sitebot') !== false) {
        return 'SiteBot';
    }
    if (strpos($useragent, 'gosospider') !== false) {
        return 'gosospider';
    }
    if (strpos($useragent, 'gigabot') !== false) {
        return 'Gigabot';
    }
    if (strpos($useragent, 'yrspider') !== false) {
        return 'YRSpider';
    }
    if (strpos($useragent, 'gigabot') !== false) {
        return 'Gigabot';
    }
    if (strpos($useragent, 'wangidspider') !== false) {
        return 'WangIDSpider';
    }
    if (strpos($useragent, 'foxspider') !== false) {
        return 'FoxSpider';
    }
    if (strpos($useragent, 'docomo') !== false) {
        return 'DoCoMo';
    }
    if (strpos($useragent, 'yandexbot') !== false) {
        return 'YandexBot';
    }
    if (strpos($useragent, 'sinaweibobot') !== false) {
        return 'SinaWeiboBot';
    }
    if (strpos($useragent, 'catchbot') !== false) {
        return 'CatchBot';
    }
    if (strpos($useragent, 'surveybot') !== false) {
        return 'SurveyBot';
    }
    if (strpos($useragent, 'dotbot') !== false) {
        return 'DotBot';
    }
    if (strpos($useragent, 'purebot') !== false) {
        return 'Purebot';
    }
    if (strpos($useragent, 'ccbot') !== false) {
        return 'CCBot';
    }
    if (strpos($useragent, 'mlbot') !== false) {
        return 'MLBot';
    }
    if (strpos($useragent, 'adsbot-google') !== false) {
        return 'AdsBot-Google';
    }
    if (strpos($useragent, 'ahrefsbot') !== false) {
        return 'AhrefsBot';
    }
    if (strpos($useragent, 'spbot') !== false) {
        return 'spbot';
    }
    if (strpos($useragent, 'augustbot') !== false) {
        return 'AugustBot';
    }
    return false;
}

function cc_defender($CC_Defender)
{
    $iptoken = md5(x_real_ip() . date('Ymd')) . md5(time() . rand(11111, 99999));
    if ($CC_Defender == 2 || !isset($_COOKIE['ccsafe_defend']) || !preg_match("/^[a-zA-Z0-9]{64}$/", $_COOKIE['ccsafe_defend'])) {
        $x          = new \core\HieroGlyphy();
        $iptoken_js = $x->hieroglyphyString($iptoken);
        if ($_COOKIE['ccsafe_defend_time'] >= 10) {
            setcookie('ccsafe_defend_time', 0, time() + 86400);
        }

        if (empty($_COOKIE['ccsafe_defend'])) {
            $ccsafe_defend_time = $_COOKIE['ccsafe_defend_time'] + 1;
            header('Content-type:text/html;charset=utf-8');
            if ($ccsafe_defend_time >= 10) {
                exit('该浏览器不支持COOKIE或者不正常访问！');
            }
            echo '<html><head><meta http-equiv="pragma" content="no-cache"><meta http-equiv="cache-control" content="no-cache"><meta http-equiv="content-type" content="text/html;charset=utf-8"><title>安全验证中</title><script>console.log("chenmYun CC防御系统");function getCookie(name){var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");if(arr=document.cookie.match(reg))return unescape(arr[2]);else return null;}function setCookie(name,value){var exp = new Date();exp.setTime(exp.getTime() + 60*60*1000);document.cookie = name + "="+ escape (value).replace(/\+/g, \'%2B\') + ";expires=" + exp.toGMTString() + ";path=/";}var ccsafe_defend_time=getCookie(\'ccsafe_defend_time\')||0;ccsafe_defend_time++;setCookie(\'ccsafe_defend\',' . $iptoken_js . ');setCookie(\'ccsafe_defend_time\',ccsafe_defend_time);if(ccsafe_defend_time>1)window.location.href="./index.php";else window.location.reload();</script></head><body>安全验证中...<br><noscript>该浏览器支持JavaScript脚本，请先开启支持!</noscript></body></html>';
            die;
        }
    }

    if ($CC_Defender == 3 && !preg_match("/^[a-zA-Z0-9]{64}$/", $_COOKIE['client_token'])) {
        $client_token = md5(x_real_ip() . date('YmdHis') . 'Qq 857285711') . md5(time() . rand(11111, 99999));
        $x            = new \core\HieroGlyphy();
        $sec_token_js = $x->hieroglyphyString($client_token);
        if ($_COOKIE['client_token_time'] >= 10) {
            setcookie('client_token_time', 0, time() + 86400);
        }
        $client_token_time = $_COOKIE['client_token_time'] + 1;
        header('Content-type:text/html;charset=utf-8');
        if ($client_token_time >= 10) {
            exit('浏览器不支持COOKIE，请更换浏览器再试！');
        }

        echo '<html><head><meta http-equiv="pragma" content="no-cache"><meta http-equiv="cache-control" content="no-cache"><meta http-equiv="content-type" content="text/html;charset=utf-8"><title>安全验证中</title><script>var client_token=' . $sec_token_js . ';console.log("chenmYun CC防御系统");function setCookie(name,value){var exp = new Date();exp.setTime(exp.getTime() + 60*60*1000);document.cookie = name + "="+ escape (value).replace(/\+/g, \'%2B\') + ";expires=" + exp.toGMTString() + ";path=/";}function getCookie(name){var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");if(arr=document.cookie.match(reg))return unescape(arr[2]);else return null;}var client_token_time=getCookie(\'client_token_time\')||0;client_token_time++;setCookie(\'client_token\',client_token);setCookie(\'client_token_time\',client_token_time);if(client_token_time>1){setTimeout(function(){window.location.href="./index.php";},1000);}else{setTimeout(function(){window.location.reload();},1000);}</script></head><body>安全验证中...<br><noscript>该页面需要浏览器支持JavaScript脚本才能正常显示!</noscript></body></html>';
        die;
    } else {
        setcookie('sec_token_time', 0, time() + 86400);
    }
}

function getAddSitePirce($power = 0, $zid = 1)
{
    global $DB, $conf;
    $zid = intval($zid);
    if (!isset($conf['fenzhan_price']) || !isset($conf['fenzhan_price2'])) {
        try {
            $rs = $DB->select("SELECT * FROM `pre_config`");
            if ($rs) {
                $conf = $rs;
            }
        } catch (\PDOException $e) {
            //
        }
    }
    if ($zid <= 1) {
        if ($power == 2) {
            return sprintf('%.2f', $conf['fenzhan_price2']);
        } elseif ($power == 1) {
            return sprintf('%.2f', $conf['fenzhan_price']);
        } else {
            return 0;
        }
    } else {
        $row = $DB->get_row("SELECT * FROM `pre_site` WHERE `zid`='{$zid}' limit 1");
        if ($row['zid'] && $row['power'] > 0) {
            if ($power == 2) {
                if ($row['ktfz_price2'] >= $conf['fenzhan_cost2']) {
                    return $row['ktfz_price2'];
                }
                return getAddSitePirce($power, 1);
            } elseif ($power == 1) {
                if ($row['ktfz_price'] >= $conf['fenzhan_cost']) {
                    return $row['ktfz_price'];
                }
                return getAddSitePirce($power, 1);
            } else {
                return 0;
            }
        } else {
            return getAddSitePirce($power, 1);
        }
    }
}

function getUpdateSitePirce($power = 1, $zid = 1)
{
    global $DB, $conf;
    $zid = intval($zid);
    if ($zid == 1) {
        return 0;
    }
    //当前站点
    $row1 = $DB->get_row("SELECT * FROM `pre_site` WHERE `zid`='{$zid}' limit 1");
    if ($row1['power'] == 2) {
        return 0;
    } else {
        $upsite = $DB->get_row("SELECT * FROM `pre_site` WHERE `zid`='{$row1['upzid']}' limit 1");
        if ($row1['upzid'] > 0 && is_array($upsite)) {
            $price = getAddSitePirce(2, $upsite['zid']);
            $cost  = getAddSitePirce(1, $upsite['zid']);
        } else {
            $price = getAddSitePirce(2, 1);
            $cost  = getAddSitePirce(1, 1);
        }
        return sprintf('%.2f', $price - $cost);
    }
}

function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
{
    $ckey_length   = 4;
    $key           = md5($key ? $key : ENCRYPT_KEY);
    $keya          = md5(substr($key, 0, 16));
    $keyb          = md5(substr($key, 16, 16));
    $keyc          = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';
    $cryptkey      = $keya . md5($keya . $keyc);
    $key_length    = strlen($cryptkey);
    $string        = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
    $string_length = strlen($string);
    $result        = '';
    $box           = range(0, 255);
    $rndkey        = array();
    for ($i = 0; $i <= 255; $i++) {
        $rndkey[$i] = ord($cryptkey[$i % $key_length]);
    }
    for ($j = $i = 0; $i < 256; $i++) {
        $j       = ($j + $box[$i] + $rndkey[$i]) % 256;
        $tmp     = $box[$i];
        $box[$i] = $box[$j];
        $box[$j] = $tmp;
    }
    for ($a = $j = $i = 0; $i < $string_length; $i++) {
        $a       = ($a + 1) % 256;
        $j       = ($j + $box[$a]) % 256;
        $tmp     = $box[$a];
        $box[$a] = $box[$j];
        $box[$j] = $tmp;
        $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
    }
    if ($operation == 'DECODE') {
        if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
            return substr($result, 26);
        } else {
            return '';
        }
    } else {
        return $keyc . str_replace('=', '', base64_encode($result));
    }
}

function checkIfActive($string)
{
    $array     = explode(",", $string);
    $filepath  = substr($_SERVER["REQUEST_URI"], strrpos($_SERVER["REQUEST_URI"], "/") + 1, strlen($_SERVER["REQUEST_URI"]) - strrpos($_SERVER["REQUEST_URI"], "/") - 1);
    $php_self  = substr($filepath, 0, strrpos($filepath, "."));
    $php_param = '';
    if (stripos($filepath, '=') !== false) {
        if (stripos($filepath, '&') !== false) {
            $php_param = substr($filepath, stripos($filepath, "=") + 1, stripos($filepath, "&") - stripos($filepath, "=") - 1);
        } else {
            $php_param = substr($filepath, stripos($filepath, "=") + 1, strlen($filepath) - stripos($filepath, "=") - 1);
        }
    }

    if ($php_self != "" && $php_param != "") {
        if (in_array($php_self . '.' . $php_param, $array)) {
            return "active";
        }
    } else {
        if ($php_self != "" && in_array($php_self, $array)) {
            return "active";
        } elseif ($php_param != "" && in_array($php_param, $array)) {
            return "active";
        }
    }

    if ($string == "" && $php_self == "") {
        return "active";
    }

    return null;
}

function hook($action, $options = 'index', $callFunc = null)
{
    try {
        \core\Plugin::load($action, $options, $callFunc);
    } catch (\Exception $e) {
        showErrPage($e->getMessage());
    }
}

function checkIsAdminOrUser()
{
    $path = getcwd();
    if (is_file($path . '/api.php') || is_file($path . '/dbconfig.php')) {
        return false;
    }
    return true;
}

function saveSetting($k, $v)
{
    global $DB;
    $k = daddslashes($k);
    $v = daddslashes($v);

    if (is_array($v)) {
        $v = json_encode($v, 256);
    }

    if ($DB->find("SELECT * FROM `pre_config` WHERE `k`='{$k}'")) {
        return $DB->query("UPDATE `pre_config` SET `v`='{$v}' WHERE `k`='{$k}'");
    } else {
        return $DB->query("INSERT INTO `pre_config` (`k`, `v`) VALUES (:k, :v)", [':k' => $k, ':v' => $v]);
    }
}

function getSysField($field)
{
    // 映射系统字段名到实际数据库字段名
    $fieldMap = [
        'fenzhan_url' => 'siteurl',
        'fenzhan_url2' => 'siteurl2'
    ];

    return isset($fieldMap[$field]) ? $fieldMap[$field] : $field;
}

function getUserRmb()
{
    global $userrow;
    return isset($userrow['money']) ? floatval($userrow['money']) : 0;
}

function recordSiteVisit($zid, $domain)
{
    global $DB;

    // 只在非AJAX请求时记录访问
    if (defined('IS_AJAX') && IS_AJAX) {
        return;
    }

    $today = date('Y-m-d');

    // 使用 INSERT ... ON DUPLICATE KEY UPDATE 来更新访问统计
    $sql = "INSERT INTO `pre_site_stats` (`zid`, `domain`, `visit_count`, `last_visit`, `date_created`)
            VALUES (?, ?, 1, NOW(), ?)
            ON DUPLICATE KEY UPDATE
            `visit_count` = `visit_count` + 1,
            `last_visit` = NOW()";

    try {
        $DB->query($sql, [$zid, $domain, $today]);
    } catch (Exception $e) {
        // 静默处理错误，不影响正常访问
    }
}

function webInit()
{
    global $DB, $is_fenzhan, $siterow, $conf;
    $url    = addslashes($_SERVER['HTTP_HOST']);
    $field  = getSysField('fenzhan_url');
    $field2 = getSysField('fenzhan_url2');
    if (!isset($siterow['zid'])) {
        // 首先尝试匹配主域名和第二域名
        $siterow = $DB->get_row("SELECT * from `pre_site` where `{$field}`='{$url}' OR `{$field2}`='{$url}' limit 1");

        // 如果没有找到，则搜索 domains_extra 字段中的额外域名
        if (!$siterow) {
            $siterow = $DB->get_row("SELECT * from `pre_site` where `domains_extra` IS NOT NULL AND `domains_extra` != '' AND JSON_SEARCH(`domains_extra`, 'one', '{$url}') IS NOT NULL limit 1");
        }
    }

    if ($siterow && $siterow['power'] > 0) {
        if (stripos($conf['zz_fenzhan_remain'], ',') !== false) {
            $fenzhan_remain = explode(',', $conf['zz_fenzhan_remain']);
        } else {
            $fenzhan_remain = array($conf['zz_fenzhan_remain']);
        }

        if (in_array($url, $fenzhan_remain)) {
            $DB->query("DELETE FROM `pre_site` WHERE `{$field}`= ? or `{$field2}`= ?", array($url, $url));
            exit("<script language='javascript'>window.location.reload();</script>");
        }

        if ($siterow['status'] == 1) {
            $is_fenzhan = true;

            // 记录分站访问统计
            recordSiteVisit($siterow['zid'], $url);

            if ($conf['index_unify'] == 1) {
                $siterow['kfqq']  = $conf['kfqq'];
                $siterow['kfqq2'] = $conf['kfqq2'];
            }

            if (empty($siterow['kfqq'])) {
                $siterow['kfqq'] = $siterow['qq'];
            }

            if (intval($conf['fenzhan_gonggao_open']) !== 1) {
                unset($siterow['anounce']);
                unset($siterow['bottom']);
                unset($siterow['modal']);
                unset($siterow['alert']);
            }

            if (function_exists('getAddSitePirce')) {
                $conf['fenzhan_price']  = getAddSitePirce(1, $siterow['zid']);
                $conf['fenzhan_price2'] = getAddSitePirce(2, $siterow['zid']);
            } else {
                $conf['fenzhan_price'] = $siterow['ktfz_price'];
                if ($siterow['ktfz_price'] < $conf['fenzhan_cost']) {
                    $conf['fenzhan_price'] = $conf['fenzhan_cost'];
                }

                if ($siterow['power'] >= 2 && $siterow['ktfz_price2'] > $conf['fenzhan_cost2'] && $conf['fenzhan_cost2'] > 0) {
                    $conf['fenzhan_price2'] = $siterow['ktfz_price2'];
                }
            }

            $conf = array_merge($conf, $siterow);
        }
    } else {
        if (empty($conf['kfqq'])) {
            $conf['kfqq'] = $conf['qq'];
        }

        if (empty($conf['zzqq'])) {
            $conf['zzqq'] = $conf['kfqq'];
        }

        if (empty($conf['qq'])) {
            $conf['qq'] = $conf['zzqq'];
        }
    }

    if (empty($conf['kfname'])) {
        $conf['kfname'] = '在线客服';
    }

    if (empty($conf['kfname2'])) {
        $conf['kfname2'] = '在线客服';
    }
}

/**
 * 获取web相对路径 返回
 * @return string 返回../或./
 */
function getWebPath()
{

    global $siteurl;
    preg_match('/:\/\/([\w\-\/\.]+)/', $siteurl, $match);
    preg_match('/:\/\/([\w\-\.]+)/', $siteurl, $match2);
    //如果匹配的结果相等表示当前访问的是首页，否则表示访问的是主站后台或用户后台目录
    if ($match2[1] . '/' != $match[1]) {
        return '../';
    }
    return './';
}

function syshow($msg = '系统提示')
{
    header('Content-type:text/html;charset=utf-8');
    echo '<html><body><br/><br/><div><center><h4>神秘人云商城Plus提示您</h4><p>' . $msg . '</p></center></div></body></html>';
    die;
}

if (!file_exists(ROOT . 'install/install.lock') && file_exists(ROOT . 'install/index.php')) {
    sysmsg('<h2>检测到无 install.lock 文件</h2><ul><li><font size="4">如果您尚未安装本程序，请<a href="' . getWebPath() . 'install/">前往安装</a></font></li><li><font size="4">如果您已经安装本程序，请手动放置一个空的 install.lock 文件到 /install 文件夹下，<b>为了您站点安全，在您完成它之前我们不会工作。</b></font></li></ul><br/><h4>为什么必须建立 install.lock 文件？</h4>它是程序的保护文件，如果检测不到它，就会认为站点还没安装，此时任何人都可以安装/重装本系统。<br/><br/>', true);
}

function getAdminDir($path = '')
{
    global $isLogin;
    if (!$isLogin) {
        return '';
    }
    if ($path == '') {
        $path = str_replace('\\', '/', $_SERVER['SCRIPT_NAME']);
    }
    if (strpos($path, '?')) {
        $path = explode('?', $path, 2)[0];
    }
    $path = substr($path, 0, strrpos($path, '/'));
    $dir  = substr($path, strrpos($path, '/') + 1, strlen($path));
    return $dir;
}

function getWebWrl()
{
    global $sitepath, $isLogin, $isLogin2, $isLogin3;

    $sitepath = trim($sitepath, '/');
    $protocol = (HTTPS_ROOT ? 'https' : 'http') . '://';
    if ($isLogin == 1 && substr_count($sitepath, '/') > 1) {
        $url = $protocol . $_SERVER['HTTP_HOST'] . str_replace(dirname(ROOT), '', ROOT);
    } else {
        if (substr_count($sitepath, '/') > 0 || !empty($sitepath)) {
            if ($isLogin == 1 && !empty($sitepath) && substr_count($sitepath, '/') == 0) {
                //  说明是后台
                $url = $protocol . $_SERVER['HTTP_HOST'] . '/';
            } elseif (in_array($sitepath, ['user', 'sup', 'other'])) {
                //  说明是用户后台和供货商后台、支付目录
                $url = $protocol . $_SERVER['HTTP_HOST'] . '/';
            } else {
                $url = $protocol . $_SERVER['HTTP_HOST'] . str_replace(dirname(ROOT), '', ROOT);
            }
        } else {
            $url = $protocol . $_SERVER['HTTP_HOST'] . '/';
        }
    }
    return $url;
}

$cookiesid = $_COOKIE['mysid'];
if ($cookiesid == "" || !preg_match('/^[0-9a-z]{32}$/i', $cookiesid)) {
    $cookiesid = md5(uniqid(mt_rand(), 1) . time() . rand(1111, 9999) . x_real_ip());
    setcookie('mysid', $cookiesid, time() + 86400 * 30, '/');
}

$kfInfo[] = array('qq' => $conf['kfqq'], 'kfqq' => $conf['kfqq'], 'name' => $conf['kfname']);
if ($conf['kfqq2']) {
    $kfInfo[] = array('qq' => $conf['kfqq2'], 'kfqq' => $conf['kfqq2'], 'name' => $conf['kfname2']);
}
$clientip = x_real_ip();

if (filesize(SYSTEM_ROOT . 'ajax.class.php') < 10240) {
    showErrPage("程序核心文件已丢失或未更新到最新版，请下载更新包覆盖");
}

include_once SYSTEM_ROOT . 'function.php';
include_once __DIR__ . '/think/captcha/helper.php';
include_once SYSTEM_ROOT . 'member.php';

$weburl = getWebWrl();

define('WEB_URL', $weburl);

if (!IS_AJAX && $isLogin == 1 && is_file(ROOT . 'install/update.php')) {
    if (is_array($version_info)) {
        if ($version_info['build_sql'] > $conf['SQLVERSION']) {
            //更新数据库
            syshow('检测到有新的数据库待更新，<a href="' . getWebPath() . 'install/update.php?build=' . $version_info['build_core'] . '&goto=' . $siteurl . '">点我立即完成更新</a>');
        }

        if ($version_info['build_core'] > $conf['version']) {
            //更新系统版本
            $conf['version'] = $version_info['build_core'];
            saveSetting('version', $version_info['build_core']);
        }
    }
}

include_once SYSTEM_ROOT . 'lib.class.php';

//站点配置初始化
webInit();

if ($webConfig['debug'] && !$isLogin2) {
    $LOG = new \core\Log(1, 15, 'System');
    $LOG->add('系统日志', '');
} else {
    $LOG = new \core\Log($isLogin2 == true ? $userrow['zid'] : 1);
}

if ($debug) {
    $temp_log = new \core\Log(1, 7, 'Debug');
    $temp_log->setWriteParams(false);
    $temp_log->add('站点信息', "|-域名：" . addslashes($_SERVER['HTTP_HOST']) . "\n|-域名字段：" . getSysField('fenzhan_url') . "\n|-row：" . json_encode($siterow) . "\n|-是否分站：" . ($is_fenzhan ? '是' : '否') . "\n|-分站ID：" . $siterow['zid']);
}

$act = isset($_GET['act']) ? $_GET['act'] : '';
if (IS_AJAX && in_array($act, ['getcount', 'captcha', 'getleftcount'])) {
    //加载性能优化
} else {
    include_once SYSTEM_ROOT . 'func.class.php';
}

if ($isLogin === 1 && isset($loadGoodLib) && $loadGoodLib == true) {
    //加载性能优化
    include_once SYSTEM_ROOT . 'possess.class.php';
}

$cdnpublic = '';
if ($conf['cdnpublic'] == (0 - 1)) {
    if (preg_match('/^(http|https):\/\/[\w\.\-]+|[\w\/]+$/', $conf['cdnpublic'])) {
        $cdnpublic = $conf['cdnpublic_url'];
    }
} elseif ($conf['cdnpublic'] == 1) {
    $cdnpublic = '//lib.baomitu.com/';
}

if ($cdnpublic == '') {
    if (checkIsAdminOrUser()) {
        $cdnpublic = "../assets/public/";
    } else {
        $cdnpublic = './assets/public/';
    }
}

if (checkIsAdminOrUser()) {
    $cdnserver = "../";
} else {
    $cdnserver = "./";
}

if ($conf['cdnserver_url']) {
    if ($conf['cdnserver_url'] === '/' || preg_match('/^(http|https):\/\/[\w\.\-]+\.[\w]+$/', $conf['cdnserver_url'])) {
        $cdnserver = $conf['cdnserver_url'];
    }
}

if ($conf['captcha_open'] == 1) {
    if (empty($conf['captcha_id'])) {
        $conf['captcha_id'] = '0ea798b5bed2f1e44363199aadfc2773';
    }
    if (empty($conf['captcha_key'])) {
        $conf['captcha_key'] = '8df6fd729fd29d69f41c36bbb14483ca';
    }
}

$is_mb = checkmobile();

$conf['mobile'] = intval($is_mb);

if ($conf['fenzhan_imglimit'] < 500) {
    $conf['fenzhan_imglimit'] = 1000;
}

$conf['fenzhan_imglimit'] = $conf['fenzhan_imglimit'] * 1000;

$background_image = get_background_image();

if ($is_fenzhan == true && $conf['fenzhan_logo_open'] == 1 && $siterow['logo']) {
    $logo = parse_image($siterow['logo']);
} else {
    $logo = $conf['zz_logo'];
}

if ($logo == "") {
    $logo = 'assets/img/logo.png';
}

$logo = $logo . '?' . $jsver;

if ($conf['ui_background'] == 0) {
    $repeat = 'background-repeat:repeat;';
} elseif ($conf['ui_background'] == 1) {
    $repeat = "background-repeat:repeat-x;background-size:auto 100%;";
} elseif ($conf['ui_background'] == 2) {
    $repeat = "background-repeat:repeat-y;background-size:100% auto;";
} elseif ($conf['ui_background'] == 3) {
    $repeat = "background-repeat:no-repeat;background-size:100% 100%;";
}

if (function_exists('hook')) {
    hook('system_after');
}

/**
 * 敏感信息脱敏处理函数
 * @param string $data 需要脱敏的数据
 * @param string $type 数据类型：phone(手机号)、email(邮箱)、qq(QQ号)、auto(自动识别)
 * @return string 脱敏后的数据
 */
function maskSensitiveData($data, $type = 'auto') {
    if (empty($data)) {
        return $data;
    }

    
    if ($type === 'auto') {
        if (preg_match('/^1[3-9]\d{9}$/', $data)) {
            $type = 'phone';
        } elseif (preg_match('/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $data)) {
            $type = 'email';
        } elseif (preg_match('/^\d{5,12}$/', $data)) {
            $type = 'qq';
        } else {
            $type = 'general';
        }
    }

    switch ($type) {
        case 'phone':
           
            if (strlen($data) === 11) {
                return substr($data, 0, 3) . '****' . substr($data, -4);
            }
            break;

        case 'email':
            
            $parts = explode('@', $data);
            if (count($parts) === 2) {
                $username = $parts[0];
                $domain = $parts[1];
                if (strlen($username) > 3) {
                    $maskedUsername = substr($username, 0, 3) . '***';
                } else {
                    $maskedUsername = $username[0] . '***';
                }
                return $maskedUsername . '@' . $domain;
            }
            break;

        case 'qq':
            
            if (strlen($data) >= 5) {
                return substr($data, 0, 3) . '***' . substr($data, -3);
            }
            break;

        case 'general':
        default:
           
            if (strlen($data) > 4) {
                return substr($data, 0, 2) . '***' . substr($data, -2);
            } elseif (strlen($data) > 2) {
                return $data[0] . '***' . substr($data, -1);
            }
            break;
    }

    return $data;
}

/**
 * 检查是否需要对订单信息进行脱敏处理
 * @param array $order 订单信息
 * @param bool $isBackend 
 * @return bool 是否需要脱敏
 */
function shouldMaskOrderData($order, $isBackend = false) {
    global $conf, $is_fenzhan, $sitezid;

    
    if ($conf['order_privacy_protection'] != 1) {
        return false;
    }

   
    if ($isBackend) {
        return false;
    }

    
    if ($is_fenzhan === true) {
        return false;
    }

   
    return isset($order['zid']) && $order['zid'] > 1;
}

unset($version_info);